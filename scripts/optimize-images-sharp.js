#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Advanced Image Optimization Script using Sharp
 *
 * This script provides comprehensive image optimization for ecommerce applications:
 * - Compresses JPG, PNG, and WebP images to target file sizes (under 100KB)
 * - Converts images to WebP format with JPEG fallback support
 * - Uses adaptive quality reduction to meet size targets while preserving visual quality
 * - Optimizes for Core Web Vitals (LCP, INP) and ecommerce-quality visuals
 * - Preserves directory structure and provides detailed statistics
 * - Skips SVG files (already optimized vector graphics)
 *
 * Usage:
 *   node scripts/optimize-images-sharp.js [options]
 *
 * Options:
 *   --sourceDir=DIR       Source directory (default: public/images)
 *   --quality=N           Initial quality level (1-100, default: 80)
 *   --target-size=N       Target file size in KB (default: 80, max: 100)
 *   --webp-only           Only create WebP versions without modifying originals
 *   --progressive         Use progressive JPEG encoding
 *   --help                Show this help message
 *
 * Examples:
 *   node scripts/optimize-images-sharp.js --target-size=50
 *   node scripts/optimize-images-sharp.js --target-size=30
 *   node scripts/optimize-images-sharp.js --webp-only --quality=85
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Parse command line arguments
const args = process.argv.slice(2);
const helpArg = args.find((arg) => arg === '--help');
const webpOnlyArg = args.find((arg) => arg === '--webp-only');
const progressiveArg = args.find((arg) => arg === '--progressive');
const qualityArg = args.find((arg) => arg.startsWith('--quality='));
const sourceDirArg = args.find((arg) => arg.startsWith('--sourceDir='));
const targetSizeArg = args.find((arg) => arg.startsWith('--target-size='));

const quality = qualityArg ? parseInt(qualityArg.split('=')[1], 10) : 80;
const targetSizeKB = targetSizeArg ? parseInt(targetSizeArg.split('=')[1], 10) : 80;
const sourceDir = sourceDirArg
  ? path.join(process.cwd(), sourceDirArg.split('=')[1])
  : path.join(process.cwd(), 'public', 'images');

// Show help if requested
if (helpArg) {
  console.log(`
Advanced Image Optimization Script using Sharp

Provides comprehensive image optimization for ecommerce applications:
- Compresses JPG, PNG, and WebP images to target file sizes (under 100KB)
- Converts images to WebP format with JPEG fallback support
- Uses adaptive quality reduction to meet size targets while preserving visual quality
- Optimizes for Core Web Vitals (LCP, INP) and ecommerce-quality visuals
- Preserves directory structure and provides detailed statistics
- Skips SVG files (already optimized vector graphics)

Usage:
  node scripts/optimize-images-sharp.js [options]

Options:
  --sourceDir=DIR       Source directory (default: public/images)
  --quality=N           Initial quality level (1-100, default: 80)
  --target-size=N       Target file size in KB (default: 80, max: 100)
  --webp-only           Only create WebP versions without modifying originals
  --progressive         Use progressive JPEG encoding
  --help                Show this help message

Examples:
  node scripts/optimize-images-sharp.js --target-size=50
  node scripts/optimize-images-sharp.js --target-size=30
  node scripts/optimize-images-sharp.js --webp-only --quality=85
  `);
  process.exit(0);
}

// Configuration
const config = {
  quality,
  targetSizeKB,
  webpOnly: !!webpOnlyArg,
  progressive: !!progressiveArg,
  sourceDir,
  imageExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
};

// Statistics
const stats = {
  processed: 0,
  skipped: 0,
  webpConverted: 0,
  originalOptimized: 0,
  errors: 0,
  totalSavings: 0,
};

// Get file size in KB
function getFileSizeInKB(filePath) {
  const stats = fs.statSync(filePath);
  return stats.size / 1024;
}

// Get buffer size in KB
function getBufferSizeInKB(buffer) {
  return buffer.length / 1024;
}

// Optimize image with adaptive quality reduction to meet target size
async function optimizeImageToTargetSize(
  inputPath,
  outputPath,
  format,
  targetSizeKB,
  initialQuality = 80
) {
  let quality = initialQuality;
  let buffer;
  let attempts = 0;
  const maxAttempts = 10;
  const minQuality = 20;

  while (attempts < maxAttempts && quality >= minQuality) {
    try {
      const sharpInstance = sharp(inputPath);

      if (format === 'jpeg') {
        buffer = await sharpInstance
          .jpeg({
            quality,
            mozjpeg: true,
            progressive: config.progressive,
            optimiseScans: true,
            trellisQuantisation: true,
            overshootDeringing: true,
          })
          .toBuffer();
      } else if (format === 'png') {
        buffer = await sharpInstance
          .png({
            quality,
            compressionLevel: 9,
            adaptiveFiltering: true,
            palette: true,
          })
          .toBuffer();
      } else if (format === 'webp') {
        buffer = await sharpInstance
          .webp({
            quality,
            effort: 6,
            smartSubsample: true,
            reductionEffort: 6,
          })
          .toBuffer();
      } else {
        throw new Error(`Unsupported format: ${format}`);
      }

      const sizeKB = getBufferSizeInKB(buffer);

      if (sizeKB <= targetSizeKB) {
        // Target size achieved, save the file
        fs.writeFileSync(outputPath, buffer);
        return { success: true, finalQuality: quality, finalSize: sizeKB };
      }

      // Reduce quality for next attempt
      quality -= 10;
      attempts++;
    } catch (error) {
      throw new Error(`Failed to optimize image: ${error.message}`);
    }
  }

  // If we couldn't reach target size, save the last attempt
  if (buffer) {
    fs.writeFileSync(outputPath, buffer);
    const finalSize = getBufferSizeInKB(buffer);
    return { success: false, finalQuality: quality + 10, finalSize };
  }

  throw new Error('Failed to optimize image after all attempts');
}

// Process a single image file
async function processImage(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const dir = path.dirname(filePath);
  const baseName = path.basename(filePath, ext);
  const webpPath = path.join(dir, `${baseName}.webp`);

  try {
    // Skip SVG files
    if (ext === '.svg') {
      console.log(`Skipping SVG: ${filePath}`);
      stats.skipped++;
      return;
    }

    // Get original file size
    const originalSize = getFileSizeInKB(filePath);

    // Create WebP version for all image types with target size optimization
    if (ext === '.jpg' || ext === '.jpeg' || ext === '.png' || ext === '.webp') {
      const webpResult = await optimizeImageToTargetSize(
        filePath,
        webpPath,
        'webp',
        config.targetSizeKB,
        config.quality
      );

      const webpSize = webpResult.finalSize;
      const webpSavings = originalSize - webpSize;

      console.log(
        `Created WebP: ${webpPath} (${webpSize.toFixed(2)} KB, quality: ${webpResult.finalQuality}, saved ${webpSavings.toFixed(2)} KB)${
          webpResult.success ? '' : ' [TARGET SIZE NOT REACHED]'
        }`
      );
      stats.webpConverted++;
      stats.totalSavings += webpSavings;
    }

    // Optimize original files if not in webp-only mode
    if (!config.webpOnly) {
      let format;
      if (ext === '.jpg' || ext === '.jpeg') {
        format = 'jpeg';
      } else if (ext === '.png') {
        format = 'png';
      } else if (ext === '.webp') {
        format = 'webp';
      }

      if (format) {
        const optimizeResult = await optimizeImageToTargetSize(
          filePath,
          filePath,
          format,
          config.targetSizeKB,
          config.quality
        );

        const optimizedSize = optimizeResult.finalSize;
        const savings = originalSize - optimizedSize;

        console.log(
          `Optimized: ${filePath} (${optimizedSize.toFixed(2)} KB, quality: ${optimizeResult.finalQuality}, saved ${savings.toFixed(2)} KB)${
            optimizeResult.success ? '' : ' [TARGET SIZE NOT REACHED]'
          }`
        );
        stats.originalOptimized++;
        stats.totalSavings += savings;
      }
    }

    stats.processed++;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

// Walk through directory recursively
async function walkDirectory(dir) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      await walkDirectory(filePath);
    } else if (stat.isFile()) {
      const ext = path.extname(filePath).toLowerCase();
      if (config.imageExtensions.includes(ext)) {
        await processImage(filePath);
      } else {
        stats.skipped++;
      }
    }
  }
}

// Main function
async function main() {
  console.log('Advanced Image Optimization Script using Sharp');
  console.log('==============================================');
  console.log(`Source directory: ${config.sourceDir}`);
  console.log(`Quality level: ${config.quality}`);
  console.log(`Target size: ${config.targetSizeKB} KB`);
  console.log(`Mode: ${config.webpOnly ? 'WebP conversion only' : 'Full optimization'}`);
  console.log(`Progressive JPEG: ${config.progressive ? 'Enabled' : 'Disabled'}`);
  console.log('==============================================');

  if (!fs.existsSync(config.sourceDir)) {
    console.error(`Error: Source directory ${config.sourceDir} does not exist.`);
    process.exit(1);
  }

  const startTime = Date.now();

  try {
    await walkDirectory(config.sourceDir);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('\nOptimization Complete');
    console.log('==============================================');
    console.log(`Processed: ${stats.processed} images`);
    console.log(`WebP conversions: ${stats.webpConverted}`);
    console.log(`Original files optimized: ${stats.originalOptimized}`);
    console.log(`Skipped: ${stats.skipped} files`);
    console.log(`Errors: ${stats.errors}`);
    console.log(
      `Total savings: ${stats.totalSavings.toFixed(2)} KB (${(stats.totalSavings / 1024).toFixed(2)} MB)`
    );
    console.log(`Duration: ${duration.toFixed(2)} seconds`);
  } catch (error) {
    console.error('Error during optimization:', error);
    process.exit(1);
  }
}

// Run the script
main();
