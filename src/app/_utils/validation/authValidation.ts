import { z } from 'zod';
import { validatePhoneNumber } from './phoneValidation';

/**
 * Validation schema for phone verification requests
 */
export const phoneVerificationSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, 'Insira um número de telefone válido com DDD.')
    .refine(
      (value) => {
        const { isValid } = validatePhoneNumber(value, '+55');
        return isValid;
      },
      {
        message: 'Insira um número de telefone válido com DDD.',
      }
    ),
});

/**
 * Validation schema for OTP confirmation requests
 */
export const otpConfirmationSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, 'Insira um número de telefone válido com DDD.')
    .refine(
      (value) => {
        const { isValid } = validatePhoneNumber(value, '+55');
        return isValid;
      },
      {
        message: 'Insira um número de telefone válido com DDD.',
      }
    ),
  otpCode: z
    .string()
    .min(1, 'Insira o código de verificação.')
    .length(6, 'O código de verificação deve ter 6 dígitos.')
    .regex(/^\d{6}$/, 'O código de verificação deve conter apenas números.'),
});

/**
 * Validation schema for FormData phone verification
 */
export const phoneVerificationFormDataSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, 'Insira um número de telefone válido com DDD.')
    .refine(
      (value) => {
        const { isValid } = validatePhoneNumber(value, '+55');
        return isValid;
      },
      {
        message: 'Insira um número de telefone válido com DDD.',
      }
    ),
});

/**
 * Validation schema for FormData OTP confirmation
 */
export const otpConfirmationFormDataSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, 'Insira um número de telefone válido com DDD.')
    .refine(
      (value) => {
        const { isValid } = validatePhoneNumber(value, '+55');
        return isValid;
      },
      {
        message: 'Insira um número de telefone válido com DDD.',
      }
    ),
  otpCode: z
    .string()
    .min(1, 'Insira o código de verificação.')
    .length(6, 'O código de verificação deve ter 6 dígitos.')
    .regex(/^\d{6}$/, 'O código de verificação deve conter apenas números.'),
});

export type PhoneVerificationInput = z.infer<typeof phoneVerificationSchema>;
export type OtpConfirmationInput = z.infer<typeof otpConfirmationSchema>;
export type PhoneVerificationFormDataInput = z.infer<typeof phoneVerificationFormDataSchema>;
export type OtpConfirmationFormDataInput = z.infer<typeof otpConfirmationFormDataSchema>;
