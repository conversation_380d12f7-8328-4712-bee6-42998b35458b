/**
 * Formats a date (string or Date object) into a localized Brazilian date format
 */
export function formatDate(date: string | Date | null | undefined): string {
  if (!date) {
    return '';
  }

  let year: number;
  let month: number;
  let day: number;

  if (date instanceof Date) {
    // If date is a Date object, extract the components directly
    year = date.getFullYear();
    month = date.getMonth() + 1; // getMonth() returns 0-11
    day = date.getDate();
  } else {
    try {
      // If date is a string, try to parse it
      if (date.includes('T')) {
        // Handle ISO format strings (e.g., "2023-06-15T00:00:00.000Z")
        const dateObj = new Date(date);
        year = dateObj.getFullYear();
        month = dateObj.getMonth() + 1;
        day = dateObj.getDate();
      } else {
        // Handle simple date strings (e.g., "2023-06-15")
        const [yearStr, monthStr, dayStr] = date.split('-').map(Number);
        year = yearStr;
        month = monthStr;
        day = dayStr;
      }
    } catch (error) {
      console.error('Error parsing date:', error);
      return '';
    }
  }

  // Validate the date components
  if (isNaN(year) || isNaN(month) || isNaN(day)) {
    console.error('Invalid date components:', { year, month, day });
    return '';
  }

  // Create a formatted date string using the parts
  const formatter = new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: 'long',
    year: 'numeric',
  });

  // Use a fixed date constructor that won't be affected by timezone
  // Month is 0-indexed in JavaScript Date
  const dateObj = new Date(year, month - 1, day);

  return formatter.format(dateObj);
}

/**
 * Formats a period string (morning/afternoon) into a human-readable format
 */
export function formatPeriod(period: string): string {
  return period === 'morning' ? 'Manhã (8h - 12h)' : 'Tarde (13h - 18h)';
}

/**
 * Truncates text to a specified number of characters and adds ellipsis
 */
export function truncateText(text: string | string[] | undefined, maxChars: number = 90): string {
  if (!text) return '';

  const formattedText = Array.isArray(text) ? text.join(', ') : text;

  if (formattedText.length <= maxChars) return formattedText;
  return formattedText.slice(0, maxChars) + '...';
}

/**
 * Formats a CPF string into a formatted string with dots and dash
 */
export const formatCPF = (cpf: string): string => {
  if (!cpf) return '';

  // Remove any non-digit characters
  const digits = cpf.replace(/\D/g, '');

  // Format as XXX.XXX.XXX-XX
  return digits.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
};
