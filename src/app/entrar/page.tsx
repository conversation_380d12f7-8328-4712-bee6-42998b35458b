import { Separator } from '@/src/app/_components';
import { AskForService } from '@/src/app/_components/Common/AskForService/AskForService';
import { ServiceNavigationMenuDesktop } from '@/src/app/_components/Common/Navigation/ServiceNavigationMenuDesktop';
import { AuthenticationForm } from '@/src/app/_components/Pages/Auth/AuthenticationForm';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Entrar | GetNinjas',
  description: 'Acesse sua conta para visualizar seus agendamentos.',
};

export default function LoginPage() {
  return (
    <>
      {/* Authentication Section with gradient background matching Figma */}
      <main
        className="flex min-h-screen w-full items-center justify-center"
        style={{
          background: 'linear-gradient(236deg, #E2E8F0 -21.73%, #FFF 41.77%)',
        }}
      >
        <div className="w-full">
          <AuthenticationForm />
        </div>
      </main>

      <Separator className="relative z-40 h-[0.5px] w-full bg-gray-300" />

      {/* Menu de Navegação de Serviços - Matching Home page structure */}
      <section className="relative z-40 mx-auto my-16 max-w-7xl border-t-gray-200 px-8 lg:px-12">
        {/* Desktop: Show heading and menu */}
        <h2 className="my-8 text-3xl font-bold text-muted-foreground">O que você precisa?</h2>
        <div className="py-10 2xl:-ml-40">
          <ServiceNavigationMenuDesktop
            className="w-full"
            containerClassName="grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-6"
            categoryClassName="mb-6"
            categoryTitleClassName="flex items-center gap-2 mb-2"
            subcategoryListClassName="ml-7 space-y-3"
            subcategoryLinkClassName="text-sm font-medium text-muted-foreground hover:text-gray-900"
          />
        </div>
      </section>

      {/* Seção de pedir serviço personalizado - Matching Home page structure - Hidden on mobile since it's included in ServiceNavigationMenuMobile */}
      <section className="relative z-40 mx-auto mb-16 hidden max-w-7xl px-8 md:block lg:px-12">
        <AskForService
          variant="custom"
          className="relative w-full overflow-hidden lg:mx-auto"
          containerClassName="relative z-10 flex flex-col items-start justify-between gap-10 lg:flex-row lg:items-center"
          titleClassName="text-2xl font-bold leading-tight sm:text-3xl"
          descriptionClassName=" max-w-2xl text-base sm:text-lg"
          buttonClassName="w-full rounded-xl bg-white px-6 py-4 text-base font-bold text-black transition duration-300 hover:bg-gray-100 sm:px-8 sm:py-6 sm:text-lg"
          showIcon={true}
        />
      </section>
    </>
  );
}
