'use client';

import { sendPhoneVerificationWrapper, verifyOtpCodeDirect } from '@/src/app/_actions/auth';
import { useAuth } from '@/src/app/_context/AuthContext';
import type { AuthError } from '@/src/app/_interfaces';
import { validatePhoneNumber } from '@/src/app/_utils/validation/phoneValidation';
import { useState } from 'react';

export function usePhoneVerification() {
  const { startPhoneVerification, verifyOtp } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<AuthError | null>(null);
  const [isVerificationSent, setIsVerificationSent] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');

  // Send verification code to phone number
  const sendVerificationCode = async (phone: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use the centralized validation utility
      const { isValid, error: validationError } = validatePhoneNumber(phone, '+55');

      if (!isValid) {
        const authError: AuthError = {
          type: 'INVALID_PHONE',
          message: validationError || 'Número de telefone inválido',
        };
        setError(authError);
        return false;
      }

      // Extract digits for API call
      const digits = phone.replace(/\D/g, '');

      // Call the Server Action directly for better error handling
      const result = await sendPhoneVerificationWrapper(digits);

      if (result.success) {
        setPhoneNumber(digits);
        setIsVerificationSent(true);
        return true;
      } else {
        setError(
          result.error || {
            type: 'SERVER_ERROR',
            message: 'Falha ao enviar o código de verificação',
          }
        );
        return false;
      }
    } catch (err) {
      const authError: AuthError = {
        type: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'Erro ao enviar código de verificação',
      };
      setError(authError);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP code
  const verifyCode = async (otp: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate OTP (basic validation)
      if (otp.length !== 6 || !/^\d+$/.test(otp)) {
        const authError: AuthError = {
          type: 'INVALID_OTP',
          message: 'Código de verificação deve ter 6 dígitos',
        };
        setError(authError);
        return false;
      }

      // Call the Server Action directly for better error handling
      const result = await verifyOtpCodeDirect(phoneNumber, otp);

      if (result.success) {
        return true;
      } else {
        setError(
          result.error || {
            type: 'INVALID_OTP',
            message: 'Código de verificação inválido',
          }
        );
        return false;
      }
    } catch (err) {
      const authError: AuthError = {
        type: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'Erro ao verificar código',
      };
      setError(authError);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Reset the verification state
  const resetVerification = () => {
    setIsVerificationSent(false);
    setPhoneNumber('');
    setError(null);
  };

  return {
    isLoading,
    error,
    isVerificationSent,
    phoneNumber,
    sendVerificationCode,
    verifyCode,
    resetVerification,
  };
}
