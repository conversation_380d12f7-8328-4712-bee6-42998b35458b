/**
 * Authentication-related constants
 */

/**
 * Authentication cookie names
 */
export const AUTH_COOKIES = {
  TOKEN: 'token',
  USER_ID: 'userId',
  PHONE_NUMBER: 'phoneNumber',
  PHONE_VERIFIED: 'phoneVerified',
} as const;

/**
 * Cookie configuration for authentication
 */
export const AUTH_COOKIE_CONFIG = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  maxAge: 7 * 24 * 60 * 60, // 7 days
  path: '/',
};

/**
 * Authentication API endpoints
 */
export const AUTH_ENDPOINTS = {
  PHONE_VERIFICATION: '/authenticator',
  OTP_CONFIRMATION: '/authenticator/confirm',
} as const;

/**
 * Authentication error types
 */
export const AUTH_ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  INVALID_PHONE: 'INVALID_PHONE',
  INVALID_OTP: 'INVALID_OTP',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

/**
 * Authentication error messages
 */
export const AUTH_ERROR_MESSAGES = {
  INVALID_PHONE: 'Número de telefone inválido',
  PHONE_NOT_FOUND: 'Número de telefone não encontrado',
  INVALID_OTP: 'Código de verificação inválido ou expirado',
  SESSION_NOT_FOUND: 'Sessão de verificação não encontrada',
  SERVER_ERROR: 'Erro interno do servidor. Tente novamente.',
  NETWORK_ERROR: 'Erro de conexão. Verifique sua internet e tente novamente.',
  PHONE_VALIDATION_ERROR: 'Número de telefone inválido. Verifique o DDD e tente novamente.',
  SEND_CODE_ERROR: 'Erro ao enviar código de verificação',
  VERIFICATION_FAILED: 'Falha ao enviar código de verificação',
  OTP_INVALID: 'Código de verificação inválido',
} as const;

/**
 * HTTP status codes for authentication
 */
export const AUTH_HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;
