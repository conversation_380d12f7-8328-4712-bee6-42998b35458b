import { Schedule } from '@/src/app/_interfaces/schedule';

/**
 * Mock data for user schedules
 * Used for development and testing of the schedules feature
 */
export const mockSchedules: Schedule[] = [
  {
    id: '12345678-1234-5678-1234-567812345678',
    serviceId: '1',
    serviceName: 'Conserto de Geladeira',
    serviceSlug: 'assistencia-tecnica/eletrodomesticos',
    date: '2023-06-15',
    time: '14:00',
    address: {
      street: 'Rua das Flores',
      number: '123',
      complement: 'Apto 101',
      neighborhood: 'Centro',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01234-567',
    },
    status: 'confirmed',
  },
  {
    id: '87654321-8765-4321-8765-432187654321',
    serviceId: '2',
    serviceName: 'Limpeza Residencial',
    serviceSlug: 'assistencia-tecnica/eletrodomesticos',
    date: '2023-06-20',
    time: '10:00',
    address: {
      street: 'Avenida Paulista',
      number: '1000',
      neighborhood: 'Bela Vista',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01310-100',
    },
    status: 'pending',
  },
  {
    id: '11111111-2222-3333-4444-555555555555',
    serviceId: '3',
    serviceName: 'Instalação de Ar Condicionado',
    serviceSlug: 'assistencia-tecnica/eletrodomesticos',
    date: '2023-05-10',
    time: '09:30',
    address: {
      street: 'Rua Augusta',
      number: '500',
      complement: 'Sala 302',
      neighborhood: 'Consolação',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01305-000',
    },
    status: 'completed',
  },
];

/**
 * Mock function to simulate fetching schedules from an API
 * @param userId The ID of the user to fetch schedules for
 * @returns A promise that resolves to an array of schedules
 */
export async function fetchUserSchedules(userId: string): Promise<Schedule[]> {
  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // In a real implementation, this would filter schedules by user ID
  // For now, we'll just return all mock schedules
  return mockSchedules;
}
