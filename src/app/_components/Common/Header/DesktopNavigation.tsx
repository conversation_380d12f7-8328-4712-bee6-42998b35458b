'use client';

import { But<PERSON>, Icon } from '@/src/app/_components';
import { ServiceNavigationMenuDesktop } from '@/src/app/_components/Common/Navigation/ServiceNavigationMenuDesktop';
import { trackClickEvent } from '@/src/app/_functions/analytics/common/trackClickEvent';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { memo, RefObject, useRef, useState } from 'react';

const NavigationMenu = dynamic(() =>
  import('@/src/app/_components/Ui/navigationMenu').then((mod) => mod.NavigationMenu)
);
const NavigationMenuList = dynamic(() =>
  import('@/src/app/_components/Ui/navigationMenu').then((mod) => mod.NavigationMenuList)
);
const NavigationMenuItem = dynamic(() =>
  import('@/src/app/_components/Ui/navigationMenu').then((mod) => mod.NavigationMenuItem)
);

interface DesktopNavigationProps {
  submenuRef: RefObject<HTMLDivElement>;
  isSubmenuOpen: boolean;
  handleMouseEnter: () => void;
  handleMouseLeave: () => void;
  pathname: string;
}

const DesktopNavigation = memo(function DesktopNavigation({}: DesktopNavigationProps) {
  const [isServiceMenuOpen, setIsServiceMenuOpen] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const showMenu = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsServiceMenuOpen(true);
  };

  const hideMenu = () => {
    timeoutRef.current = setTimeout(() => {
      setIsServiceMenuOpen(false);
    }, 100); // Delay to allow moving to the menu
  };

  return (
    <div className="hidden md:block">
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem className="relative">
            <div
              className={`relative rounded-md px-4 py-2 transition-colors duration-300 ${
                isServiceMenuOpen ? 'bg-slate-50' : 'hover:bg-slate-50'
              }`}
              onMouseEnter={showMenu}
              onMouseLeave={hideMenu}
            >
              <button className="flex cursor-pointer items-center gap-1">
                Serviços disponíveis
                <Icon
                  name="ChevronDown"
                  className={`h-4 w-4 transition-transform duration-300 ease-in-out ${
                    isServiceMenuOpen ? 'rotate-180' : ''
                  }`}
                />
              </button>
            </div>
          </NavigationMenuItem>
          <NavigationMenuItem>
            <Link
              href="https://www.getninjas.com.br/"
              className="rounded-md px-4 py-2 transition-colors duration-300 hover:bg-slate-50"
              target="_blank"
              onClick={() => trackClickEvent('GetNinjas Core')}
            >
              Outros serviços
            </Link>
          </NavigationMenuItem>
          <NavigationMenuItem>
            <Link
              href="/entrar"
              className="rounded-md px-4 py-2 transition-colors duration-300 hover:bg-slate-50"
              onClick={() => trackClickEvent('Login Button')}
            >
              <Button
                variant="outline"
                className="gap-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              >
                <span>Entrar</span>
              </Button>
            </Link>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>

      {/* Menu dropdown outside of the navigation layout */}
      {isServiceMenuOpen && (
        <div
          className="fixed left-0 top-[72px] z-[100] w-screen bg-white shadow-lg"
          onMouseEnter={showMenu}
          onMouseLeave={hideMenu}
        >
          <div className="w-full px-12 py-10">
            <ServiceNavigationMenuDesktop
              className="w-full"
              containerClassName="grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-6"
              categoryClassName="mb-6"
              categoryTitleClassName="flex items-center gap-2 mb-2"
              subcategoryListClassName="ml-7 space-y-3"
              subcategoryLinkClassName="text-sm font-medium text-muted-foreground hover:text-gray-900"
            />
          </div>
        </div>
      )}
    </div>
  );
});

export default DesktopNavigation;
