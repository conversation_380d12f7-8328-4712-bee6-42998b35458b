/**
 * Skeleton loading component for schedules list
 */

export function SchedulesSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {/* Generate 6 skeleton cards */}
      {Array.from({ length: 6 }).map((_, index) => (
        <div
          key={index}
          className="animate-pulse rounded-lg border border-gray-200 bg-white p-6 shadow-sm"
        >
          {/* Service name skeleton */}
          <div className="mb-4">
            <div className="h-6 w-3/4 rounded bg-gray-200"></div>
          </div>

          {/* Date and time skeleton */}
          <div className="mb-4 space-y-2">
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 rounded bg-gray-200"></div>
              <div className="h-4 w-24 rounded bg-gray-200"></div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 rounded bg-gray-200"></div>
              <div className="h-4 w-16 rounded bg-gray-200"></div>
            </div>
          </div>

          {/* Address skeleton */}
          <div className="mb-4 space-y-2">
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 rounded bg-gray-200"></div>
              <div className="h-4 w-full rounded bg-gray-200"></div>
            </div>
            <div className="ml-6 space-y-1">
              <div className="h-3 w-5/6 rounded bg-gray-200"></div>
              <div className="h-3 w-4/6 rounded bg-gray-200"></div>
            </div>
          </div>

          {/* Status badge skeleton */}
          <div className="mb-4">
            <div className="h-6 w-20 rounded-full bg-gray-200"></div>
          </div>

          {/* Action buttons skeleton */}
          <div className="flex space-x-2">
            <div className="h-9 w-24 rounded bg-gray-200"></div>
            <div className="h-9 w-20 rounded bg-gray-200"></div>
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * Single schedule card skeleton
 */
export function ScheduleCardSkeleton() {
  return (
    <div className="animate-pulse rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      {/* Service name skeleton */}
      <div className="mb-4">
        <div className="h-6 w-3/4 rounded bg-gray-200"></div>
      </div>

      {/* Date and time skeleton */}
      <div className="mb-4 space-y-2">
        <div className="flex items-center space-x-2">
          <div className="h-4 w-4 rounded bg-gray-200"></div>
          <div className="h-4 w-24 rounded bg-gray-200"></div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="h-4 w-4 rounded bg-gray-200"></div>
          <div className="h-4 w-16 rounded bg-gray-200"></div>
        </div>
      </div>

      {/* Address skeleton */}
      <div className="mb-4 space-y-2">
        <div className="flex items-center space-x-2">
          <div className="h-4 w-4 rounded bg-gray-200"></div>
          <div className="h-4 w-full rounded bg-gray-200"></div>
        </div>
        <div className="ml-6 space-y-1">
          <div className="h-3 w-5/6 rounded bg-gray-200"></div>
          <div className="h-3 w-4/6 rounded bg-gray-200"></div>
        </div>
      </div>

      {/* Status badge skeleton */}
      <div className="mb-4">
        <div className="h-6 w-20 rounded-full bg-gray-200"></div>
      </div>

      {/* Action buttons skeleton */}
      <div className="flex space-x-2">
        <div className="h-9 w-24 rounded bg-gray-200"></div>
        <div className="h-9 w-20 rounded bg-gray-200"></div>
      </div>
    </div>
  );
}
