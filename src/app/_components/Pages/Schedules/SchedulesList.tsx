'use client';

import { ErrorDisplay } from '@/src/app/_components';
import { ScheduleCard } from '@/src/app/_components/Pages/Schedules/ScheduleCard';
import { SchedulesSkeleton } from '@/src/app/_components/Pages/Schedules/SchedulesSkeleton';
import { useAuth } from '@/src/app/_context/AuthContext';
import { Schedule } from '@/src/app/_interfaces/schedule';
import { fetchUserSchedules } from '@/src/app/_mocks/schedules';
import { CalendarX } from 'lucide-react';
import { useEffect, useState } from 'react';

/**
 * Component for displaying user schedules
 */
export function SchedulesList() {
  const { auth } = useAuth();
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Only fetch schedules when auth loading is complete
    if (auth.isLoading) {
      return; // Wait for auth to finish loading
    }

    // Fetch schedules when the user is authenticated
    const loadSchedules = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (auth.isAuthenticated) {
          // In a real implementation, we would pass the user ID
          const userSchedules = await fetchUserSchedules(auth.userId || 'anonymous');
          setSchedules(userSchedules);
        } else {
          // User is not authenticated, clear schedules
          setSchedules([]);
        }
      } catch (err) {
        console.error('Error fetching schedules:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch schedules'));
        setSchedules([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadSchedules();
  }, [auth.isAuthenticated, auth.userId, auth.isLoading]);

  // Show loading state (either auth loading or data loading)
  if (auth.isLoading || isLoading) {
    return <SchedulesSkeleton />;
  }

  // Show error state if there was an error fetching schedules
  if (error) {
    return (
      <ErrorDisplay
        error={error}
        message="Erro ao carregar seus agendamentos. Por favor, tente novamente mais tarde."
      />
    );
  }

  // Show login prompt if user is not authenticated
  if (!auth.isAuthenticated) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <CalendarX className="mb-4 h-16 w-16 text-gray-400" />
        <h2 className="mb-2 text-xl font-bold">Você precisa estar logado</h2>
        <p className="text-gray-600">Faça login para visualizar seus agendamentos.</p>
      </div>
    );
  }

  // Show empty state if no schedules found
  if (schedules.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <CalendarX className="mb-4 h-16 w-16 text-gray-400" />
        <h2 className="mb-2 text-xl font-bold">Nenhum agendamento encontrado</h2>
        <p className="text-gray-600">Você ainda não possui agendamentos.</p>
      </div>
    );
  }

  // Show schedules grid
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {schedules.map((schedule) => (
        <ScheduleCard key={schedule.id} schedule={schedule} />
      ))}
    </div>
  );
}
