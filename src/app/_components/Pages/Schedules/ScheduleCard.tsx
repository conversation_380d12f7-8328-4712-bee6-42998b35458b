'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
  ErrorDisplay,
} from '@/src/app/_components';
import { Schedule } from '@/src/app/_interfaces/schedule';
import { formatDate } from '@/src/app/_utils';
import { Calendar, Clock, MapPin } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

interface ScheduleCardProps {
  schedule: Schedule;
  dateFormatter?: (date: string | Date | null | undefined) => string;
}

export function ScheduleCard({ schedule, dateFormatter = formatDate }: ScheduleCardProps) {
  const [error, setError] = useState<Error | null>(null);

  // Format date for display with error handling
  let formattedDate: string;
  try {
    formattedDate = dateFormatter(schedule.date);
  } catch (err) {
    console.error('Error formatting date:', err);
    setError(err instanceof Error ? err : new Error('Error formatting date'));
    formattedDate = schedule.date || '';
  }

  // If there's an error, show the error display
  if (error) {
    return <ErrorDisplay error={error} message="Erro ao formatar a data do agendamento" />;
  }

  // Get status label and color
  const getStatusInfo = (status: Schedule['status']) => {
    switch (status) {
      case 'pending':
        return { label: 'Pendente', color: 'bg-yellow-100 text-yellow-800' };
      case 'confirmed':
        return { label: 'Confirmado', color: 'bg-green-100 text-green-800' };
      case 'completed':
        return { label: 'Concluído', color: 'bg-blue-100 text-blue-800' };
      case 'cancelled':
        return { label: 'Cancelado', color: 'bg-red-100 text-red-800' };
      default:
        return { label: 'Desconhecido', color: 'bg-gray-100 text-gray-800' };
    }
  };

  const statusInfo = getStatusInfo(schedule.status);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-bold">{schedule.serviceName}</CardTitle>
          <span className={`rounded-full px-3 py-1 text-xs font-medium ${statusInfo.color}`}>
            {statusInfo.label}
          </span>
        </div>
        <CardDescription>Agendamento #{schedule.id.substring(0, 8)}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-start gap-2">
          <Calendar className="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-500" />
          <div>
            <p className="font-medium">Data</p>
            <p className="text-sm text-gray-600">{formattedDate}</p>
          </div>
        </div>
        <div className="flex items-start gap-2">
          <Clock className="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-500" />
          <div>
            <p className="font-medium">Horário</p>
            <p className="text-sm text-gray-600">{schedule.time}</p>
          </div>
        </div>
        <div className="flex items-start gap-2">
          <MapPin className="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-500" />
          <div>
            <p className="font-medium">Endereço</p>
            <p className="text-sm text-gray-600">
              {schedule.address.street}, {schedule.address.number}
              {schedule.address.complement && `, ${schedule.address.complement}`}
            </p>
            <p className="text-sm text-gray-600">
              {schedule.address.neighborhood}, {schedule.address.city} - {schedule.address.state}
            </p>
            <p className="text-sm text-gray-600">{schedule.address.zipCode}</p>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Link href={`/servicos/${schedule.serviceSlug}`} className="w-full">
          <Button variant="outline" className="w-full">
            Ver detalhes do serviço
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
