/**
 * Interface for schedule data
 */
export interface Schedule {
  id: string;
  serviceId: string;
  serviceName: string;
  serviceSlug: string;
  date: string;
  time: string;
  address: {
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
}

/**
 * Type guard to validate if an object is a Schedule
 * @param obj The object to validate
 * @returns True if the object is a valid Schedule
 */
export function isSchedule(obj: unknown): obj is Schedule {
  const schedule = obj as Partial<Schedule>;

  return (
    typeof schedule?.id === 'string' &&
    typeof schedule?.serviceId === 'string' &&
    typeof schedule?.serviceName === 'string' &&
    typeof schedule?.serviceSlug === 'string' &&
    typeof schedule?.date === 'string' &&
    typeof schedule?.time === 'string' &&
    typeof schedule?.address === 'object' &&
    typeof schedule?.address?.street === 'string' &&
    typeof schedule?.address?.number === 'string' &&
    typeof schedule?.address?.neighborhood === 'string' &&
    typeof schedule?.address?.city === 'string' &&
    typeof schedule?.address?.state === 'string' &&
    typeof schedule?.address?.zipCode === 'string' &&
    (schedule?.status === 'pending' ||
      schedule?.status === 'confirmed' ||
      schedule?.status === 'completed' ||
      schedule?.status === 'cancelled')
  );
}
