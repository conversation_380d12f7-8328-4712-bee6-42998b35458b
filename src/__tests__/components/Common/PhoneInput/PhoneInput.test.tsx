import { PhoneInput } from '@/src/app/_components/Common/PhoneInput';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock ReactCountryFlag component
jest.mock('react-country-flag', () => ({
  __esModule: true,
  default: ({ countryCode, style }: { countryCode: string; style: any }) => (
    <span data-testid="country-flag" data-country={countryCode}>
      Flag
    </span>
  ),
}));

// Mock formatPhoneNumber function
jest.mock('@/src/app/_utils/phoneFormat', () => ({
  formatPhoneNumber: jest.fn((digits: string) => {
    if (digits.length <= 2) return `(${digits}`;
    if (digits.length <= 6) return `(${digits.substring(0, 2)}) ${digits.substring(2)}`;
    if (digits.length <= 10)
      return `(${digits.substring(0, 2)}) ${digits.substring(2, 6)}-${digits.substring(6)}`;
    return `(${digits.substring(0, 2)}) ${digits.substring(2, 7)}-${digits.substring(7, 11)}`;
  }),
}));

describe('PhoneInput Component', () => {
  const mockOnChange = jest.fn();
  const mockOnValidationError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly with default props', () => {
    render(<PhoneInput value="" countryCode="+55" onChange={mockOnChange} />);

    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveValue('');
  });

  test('calls onChange when input value changes', async () => {
    render(<PhoneInput value="" countryCode="+55" onChange={mockOnChange} />);

    const inputElement = screen.getByRole('textbox');
    await userEvent.type(inputElement, '1');

    expect(mockOnChange).toHaveBeenCalledWith('(1)');
  });

  test('validates phone number on value change', async () => {
    const { rerender } = render(
      <PhoneInput
        value=""
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Simular interação do usuário primeiro
    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // Update with invalid number (too short)
    rerender(
      <PhoneInput
        value="(65) 16516515"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Wait for validation to occur
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(
        'Insira um número de telefone válido com DDD.'
      );
    });

    // Update with valid number
    rerender(
      <PhoneInput
        value="(11) 98765-4321"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Wait for validation to occur
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(null);
    });
  });

  test('calls onValidationError for invalid phone number on blur', () => {
    render(
      <PhoneInput
        value="123"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // PhoneInput no longer displays errors internally - parent component handles display
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('calls onValidationError when Brazilian phone number does not have exactly 11 digits', () => {
    render(
      <PhoneInput
        value="(11) 9876-543"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // PhoneInput no longer displays errors internally - parent component handles display
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('validates correctly when Brazilian phone number has exactly 11 digits with formatting', async () => {
    const { rerender } = render(
      <PhoneInput
        value=""
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Simular interação do usuário
    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // Aguardar a validação do campo vazio
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(
        'Insira um número de telefone válido com DDD.'
      );
    });

    // Atualizar o valor para um número válido
    rerender(
      <PhoneInput
        value="(11) 98765-4321"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Simular interação novamente
    fireEvent.change(inputElement, { target: { value: '(11) 98765-4321' } });
    fireEvent.blur(inputElement);

    // Aguardar a validação do número válido
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(null);
    });
  });

  test('calls onValidationError when input is empty on blur', () => {
    render(
      <PhoneInput
        value=""
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // PhoneInput no longer displays errors internally - parent component handles display
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('always uses Brazil country code regardless of prop', () => {
    render(
      <PhoneInput
        value=""
        countryCode="+999" // This should be ignored
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Check that Brazil flag is displayed regardless of country code prop
    expect(screen.getByTestId('country-flag')).toHaveAttribute('data-country', 'BR');
    expect(screen.getByText('+55')).toBeInTheDocument();
  });

  test('clears error when valid phone number is entered', async () => {
    const { rerender } = render(
      <PhoneInput
        value="123"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // First trigger an error
    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // PhoneInput no longer displays errors internally - parent component handles display
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );

    // Now update with valid number
    rerender(
      <PhoneInput
        value="(11) 98765-4321"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    // Wait for validation to occur
    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(null);
    });
  });

  test('calls onValidationError for invalid Brazilian DDD', () => {
    render(
      <PhoneInput
        value="(00) 98765-4321" // 00 is not a valid DDD in Brazil
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // PhoneInput no longer displays errors internally - parent component handles display
    expect(mockOnValidationError).toHaveBeenCalledWith('O DDD informado não é válido no Brasil.');
  });

  test('handles invalid phone number format', () => {
    // Use an invalid phone format that will cause validation to fail
    render(
      <PhoneInput
        value="invalid-phone-format"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // The component should handle the validation error gracefully
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('handles phone number with special characters', () => {
    // Use a phone number with special characters that might cause parsing issues
    render(
      <PhoneInput
        value="(11) 9876$-43@21"
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // The component should handle the validation error gracefully
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('handles case when onValidationError is not provided', () => {
    render(
      <PhoneInput
        value="123" // Invalid number
        countryCode="+55"
        onChange={mockOnChange}
        // No onValidationError prop
      />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.blur(inputElement);

    // Component should not crash even without onValidationError
    // PhoneInput no longer displays errors internally - parent component handles display
    expect(inputElement).toBeInTheDocument();
  });

  test('handles input change and sets hasInteracted', () => {
    render(
      <PhoneInput
        value=""
        countryCode="+55"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');

    // Change the input value
    fireEvent.change(inputElement, { target: { value: '123' } });

    // Check that onChange was called with formatted value and hasInteracted was set
    expect(mockOnChange).toHaveBeenCalledWith('(12) 3');

    // Now blur to trigger validation (which should happen because hasInteracted is true)
    fireEvent.blur(inputElement);

    // Validation error should be shown
    expect(mockOnValidationError).toHaveBeenCalledWith(
      'Insira um número de telefone válido com DDD.'
    );
  });

  test('renders without country code when showCountryCode is false', () => {
    render(<PhoneInput value="" onChange={mockOnChange} showCountryCode={false} />);

    // Country code should not be displayed
    expect(screen.queryByTestId('country-flag')).not.toBeInTheDocument();

    // Input field should still be displayed
    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toBeInTheDocument();

    // Input should have rounded corners on both sides
    expect(inputElement).toHaveClass('rounded-md');
    expect(inputElement).not.toHaveClass('rounded-r-md');
  });

  test('formats phone number using formatPhoneNumber when useFormatPhoneNumber is true', () => {
    render(<PhoneInput value="" onChange={mockOnChange} useFormatPhoneNumber={true} />);

    const inputElement = screen.getByRole('textbox');
    fireEvent.change(inputElement, { target: { value: '11987654321' } });

    // Should use the mocked formatPhoneNumber function
    expect(mockOnChange).toHaveBeenCalled();
  });

  test('calls triggerValidation when provided', () => {
    const mockTriggerValidation = jest.fn();
    render(
      <PhoneInput value="" onChange={mockOnChange} triggerValidation={mockTriggerValidation} />
    );

    const inputElement = screen.getByRole('textbox');
    fireEvent.change(inputElement, { target: { value: '11' } });

    expect(mockTriggerValidation).toHaveBeenCalled();
  });

  test('limits Brazilian phone numbers to 11 digits when pasting', () => {
    render(
      <PhoneInput value="" onChange={mockOnChange} onValidationError={mockOnValidationError} />
    );

    const inputElement = screen.getByRole('textbox');

    // Try to enter 12 digits by pasting
    fireEvent.change(inputElement, { target: { value: '119876543210' } });

    // Should truncate to 11 digits without showing an error
    expect(mockOnChange).toHaveBeenCalledWith('(11) 98765-4321');
    // Should not show an error for a valid 11-digit number
    expect(mockOnValidationError).toHaveBeenCalledWith(null);
  });

  test('prevents typing additional digits after reaching 11 digits', () => {
    // Start with a complete 11-digit number
    const { rerender } = render(
      <PhoneInput
        value="(11) 98765-4321"
        onChange={mockOnChange}
        onValidationError={mockOnValidationError}
      />
    );

    const inputElement = screen.getByRole('textbox');

    // Try to add another digit
    fireEvent.change(inputElement, { target: { value: '(11) 98765-43210' } });

    // onChange should not be called because the input is prevented
    expect(mockOnChange).not.toHaveBeenCalled();

    // The input value should remain unchanged
    expect(inputElement).toHaveValue('(11) 98765-4321');
  });

  test('formats phone number correctly when typing', () => {
    render(
      <PhoneInput value="" onChange={mockOnChange} onValidationError={mockOnValidationError} />
    );

    const inputElement = screen.getByRole('textbox');

    // Enter digits and check that onChange is called with formatted value
    fireEvent.change(inputElement, { target: { value: '11987654321' } });

    // Should format the number correctly
    expect(mockOnChange).toHaveBeenCalledWith('(11) 98765-4321');
  });
});
