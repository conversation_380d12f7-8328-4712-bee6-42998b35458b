import {
  formatBrazilianPhoneNumber,
  formatPhoneInput,
  splitBrazilianPhoneNumber,
  validatePhoneNumber,
} from '@/src/app/_utils/validation/phoneValidation';

// Mock the countries module
jest.mock('@/src/app/_utils/countries', () => ({
  countries: [
    { code: '+55', iso: 'BR' },
    { code: '+1', iso: 'US' },
  ],
}));

// Mock the validDdds module
jest.mock('@/src/app/_utils/validDdds', () => ({
  isValidBrazilianDDD: (ddd: string) => ['11', '21', '31', '41', '51'].includes(ddd),
}));

// Mock libphonenumber-js
jest.mock('libphonenumber-js', () => ({
  parsePhoneNumberFromString: jest.fn().mockImplementation((phoneNumber, countryCode) => {
    // Simple mock implementation for testing
    if (phoneNumber === 'invalid') {
      return null;
    }

    if (phoneNumber === 'throw-error') {
      throw new Error('Parsing error');
    }

    return {
      isValid: () => phoneNumber !== 'invalid-format',
      formatInternational: () => '+****************',
    };
  }),
}));

describe('Phone Validation Utilities', () => {
  describe('validatePhoneNumber', () => {
    test('returns error for empty phone number', () => {
      const result = validatePhoneNumber('');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Insira um número de telefone válido com DDD.');
    });

    test('returns error for invalid country code', () => {
      const result = validatePhoneNumber('1234567890', '+999');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Código do país inválido.');
    });

    test('validates Brazilian phone number with correct length', () => {
      // Valid mobile number (11 digits)
      const result1 = validatePhoneNumber('(11) 98765-4321');
      expect(result1.isValid).toBe(true);
      expect(result1.error).toBeNull();

      // Valid landline number (10 digits)
      const result2 = validatePhoneNumber('(11) 3456-7890');
      expect(result2.isValid).toBe(true);
      expect(result2.error).toBeNull();
    });

    test('returns error for Brazilian phone number with incorrect length', () => {
      // Too short
      const result1 = validatePhoneNumber('(11) 3456-789');
      expect(result1.isValid).toBe(false);
      expect(result1.error).toBe(
        'O número de telefone deve ter 10 ou 11 dígitos, incluindo o DDD.'
      );

      // Too long
      const result2 = validatePhoneNumber('(11) 98765-43210');
      expect(result2.isValid).toBe(false);
      expect(result2.error).toBe(
        'O número de telefone deve ter 10 ou 11 dígitos, incluindo o DDD.'
      );
    });

    test('returns error for Brazilian phone number with invalid DDD', () => {
      const result = validatePhoneNumber('(99) 98765-4321');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('O DDD informado não é válido no Brasil.');
    });

    test('handles libphonenumber validation for non-Brazilian numbers', () => {
      // Valid non-Brazilian number
      const result1 = validatePhoneNumber('(*************', '+1');
      expect(result1.isValid).toBe(true);
      expect(result1.error).toBeNull();

      // Invalid non-Brazilian number
      const result2 = validatePhoneNumber('invalid-format', '+1');
      expect(result2.isValid).toBe(false);
      expect(result2.error).toBe('Insira um número de telefone válido.');
    });

    test('handles parsing errors gracefully', () => {
      const result = validatePhoneNumber('throw-error', '+1');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Insira um número de telefone válido.');
    });
  });

  describe('formatBrazilianPhoneNumber', () => {
    test('formats mobile number correctly', () => {
      const result = formatBrazilianPhoneNumber('11987654321');
      expect(result).toBe('(11) 98765-4321');
    });

    test('formats landline number correctly', () => {
      const result = formatBrazilianPhoneNumber('1134567890');
      expect(result).toBe('(11) 3456-7890');
    });

    test('formats partial number correctly', () => {
      const result1 = formatBrazilianPhoneNumber('11');
      expect(result1).toBe('(11)');

      const result2 = formatBrazilianPhoneNumber('11987');
      expect(result2).toBe('(11) 987');
    });

    test('handles empty input', () => {
      const result = formatBrazilianPhoneNumber('');
      expect(result).toBe('');
    });
  });

  describe('formatPhoneInput', () => {
    test('formats Brazilian phone number as user types', () => {
      const result1 = formatPhoneInput('11');
      expect(result1).toBe('(11)');

      const result2 = formatPhoneInput('11987');
      expect(result2).toBe('(11) 987');

      const result3 = formatPhoneInput('11987654321');
      expect(result3).toBe('(11) 98765-4321');
    });

    test('returns input as is for non-Brazilian numbers', () => {
      const result = formatPhoneInput('5555555555', '+1');
      expect(result).toBe('5555555555');
    });

    test('handles empty input', () => {
      const result = formatPhoneInput('');
      expect(result).toBe('');
    });
  });

  describe('splitBrazilianPhoneNumber', () => {
    it('should split a valid Brazilian phone number correctly', () => {
      const result = splitBrazilianPhoneNumber('11987654321');

      expect(result).toEqual({
        phoneCode: '11',
        phoneNumber: '987654321',
        fullNumber: '(11) 98765-4321',
      });
    });

    it('should split a formatted phone number correctly', () => {
      const result = splitBrazilianPhoneNumber('(11) 98765-4321');

      expect(result).toEqual({
        phoneCode: '11',
        phoneNumber: '987654321',
        fullNumber: '(11) 98765-4321',
      });
    });

    it('should throw error for invalid phone number length', () => {
      expect(() => splitBrazilianPhoneNumber('1198765432')).toThrow(
        'Invalid Brazilian phone number: must have 11 digits'
      );
    });

    it('should throw error for invalid DDD', () => {
      expect(() => splitBrazilianPhoneNumber('99987654321')).toThrow('Invalid Brazilian DDD: 99');
    });

    it('should handle phone numbers with extra digits by truncating', () => {
      const result = splitBrazilianPhoneNumber('119876543210000');

      expect(result).toEqual({
        phoneCode: '11',
        phoneNumber: '987654321',
        fullNumber: '(11) 98765-4321',
      });
    });
  });
});
